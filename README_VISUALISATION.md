# 📊 Visualisation Graphique INDEX5

## 🎯 Vue d'ensemble

La nouvelle classe `VisualisateurMetriques` permet de générer automatiquement des **graphiques interactifs** lors de l'analyse complète d'une partie INDEX5 (option 2 du menu).

### ✨ Fonctionnalités

- **16 graphiques simultanés** : 8 métriques + 8 différentiels
- **Interactivité complète** : zoom, pan, hover avec PlotlyJS
- **Export HTML automatique** : graphiques consultables dans le navigateur
- **Couleurs optimisées** : palette professionnelle par priorité
- **Layout intelligent** : disposition 4x4 optimisée

## 🚀 Installation

### 1. Installation automatique (recommandée)

```bash
julia installer_dependances.jl
```

### 2. Installation manuelle

```julia
using Pkg
Pkg.add(["Plots", "PlotlyJS"])
```

## 📈 Utilisation

### Utilisation automatique (intégrée)

Lors de la sélection de l'option **2. Analyse complète partie réelle** :

1. **Rapport texte** généré comme avant
2. **Graphiques HTML** générés automatiquement
3. **Deux fichiers** créés :
   - `analyse_complete_partie_X_YYYYMMDD_HHMMSS.txt`
   - `graphiques_analyse_partie_X_YYYYMMDD_HHMMSS.html`

### Utilisation manuelle

```julia
# Inclure le module
include("Analyseur.jl")

# Après avoir des résultats d'analyse
visualisateur = VisualisateurMetriques{Float64}(resultats, numero_partie)
plot_complet = generer_graphiques_analyse_complete(visualisateur)
nom_fichier = exporter_graphiques_vers_html(plot_complet, numero_partie)
```

## 📊 Structure des Graphiques

### Layout 4x4 (16 sous-graphiques)

#### Ligne 1 : Métriques Priorité 1-2
- **CondT** (Priorité 1) - Bleu
- **DivKLT** (Priorité 2) - Orange  
- **CrossT** (Priorité 2) - Vert
- **MetricT** (Priorité 3) - Rouge

#### Ligne 2 : Métriques Priorité 3-5
- **TopoT** (Priorité 3) - Violet
- **TauxT** (Priorité 4) - Marron
- **ShannonT** (Priorité 5) - Rose
- **BlockT** (Priorité 5) - Gris

#### Ligne 3 : Différentiels Priorité 1-2
- **Δ CondT** - Bleu foncé
- **Δ DivKLT** - Orange foncé
- **Δ CrossT** - Vert foncé  
- **Δ MetricT** - Rouge foncé

#### Ligne 4 : Différentiels Priorité 3-5
- **Δ TopoT** - Violet foncé
- **Δ TauxT** - Marron foncé
- **Δ ShannonT** - Rose foncé
- **Δ BlockT** - Gris foncé

## 🎨 Couleurs et Priorités

### Palette Professionnelle

| Métrique | Priorité | Couleur Métrique | Couleur Différentiel |
|----------|----------|------------------|---------------------|
| CondT    | 1        | #1f77b4 (Bleu)  | #0d4f8c (Bleu foncé) |
| DivKLT   | 2        | #ff7f0e (Orange)| #cc5500 (Orange foncé)|
| CrossT   | 2        | #2ca02c (Vert)  | #1a6b1a (Vert foncé) |
| MetricT  | 3        | #d62728 (Rouge) | #a01e1e (Rouge foncé)|
| TopoT    | 3        | #9467bd (Violet)| #6b4c8a (Violet foncé)|
| TauxT    | 4        | #8c564b (Marron)| #5d3a32 (Marron foncé)|
| ShannonT | 5        | #e377c2 (Rose)  | #b55a9b (Rose foncé) |
| BlockT   | 5        | #7f7f7f (Gris)  | #4d4d4d (Gris foncé) |

## 🧪 Tests

### Test complet

```bash
julia test_visualisation.jl
```

### Test d'installation

```bash
julia installer_dependances.jl
```

## 📁 Fichiers Générés

### Fichiers de sortie automatiques

- **Texte** : `analyse_complete_partie_1_20250714_173524.txt`
- **HTML** : `graphiques_analyse_partie_1_20250714_173524.html`

### Contenu HTML

- **Graphiques interactifs** avec zoom/pan
- **Tooltips informatifs** au survol
- **Légendes claires** avec priorités
- **Responsive design** pour tous écrans

## 🔧 Dépendances

### Packages Julia requis

```toml
[deps]
Plots = "1.38+"
PlotlyJS = "0.18+"
JSON = "0.21+"
Printf = "stdlib"
Dates = "stdlib"
```

### Backends supportés

- **PlotlyJS** (recommandé) : interactivité maximale
- **GR** (fallback) : performance élevée
- **PyPlot** (alternatif) : compatibilité étendue

## 🚨 Gestion d'erreurs

### Fallbacks automatiques

1. **Erreur PlotlyJS** → Export PNG
2. **Erreur graphiques** → Rapport texte uniquement
3. **Dépendances manquantes** → Installation guidée

### Messages d'erreur

- `⚠️ Erreur lors de la génération des graphiques`
- `💡 Utilisez installer_dependances.jl`
- `🔧 Vérifiez les dépendances : Plots.jl et PlotlyJS.jl`

## 🎯 Cas d'usage

### Analyse de tendances

- **CondT décroissant** → Système devient prévisible
- **MetricT oscillant** → Changements de complexité
- **DivKLT stable** → Modèle bien adapté

### Détection de patterns

- **Différentiels faibles** → Stabilité
- **Pics de différentiels** → Changements significatifs
- **Corrélations visuelles** → Relations entre métriques

## 🔮 Évolutions futures

### Fonctionnalités prévues

- **Animations temporelles** avec Makie.jl
- **Dashboard interactif** avec Dash.jl
- **Export PDF** haute qualité
- **Comparaison multi-parties**
- **Alertes automatiques** sur seuils

### Optimisations

- **Cache des graphiques** pour performances
- **Streaming data** pour parties longues
- **GPU acceleration** avec CUDA.jl

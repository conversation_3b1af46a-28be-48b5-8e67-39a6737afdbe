"""
Script d'installation des dépendances pour la visualisation INDEX5
================================================================

Ce script installe automatiquement toutes les dépendances nécessaires
pour la visualisation graphique des métriques INDEX5.
"""

using Pkg

"""
    installer_dependances_visualisation()

Installe toutes les dépendances nécessaires pour la visualisation.
"""
function installer_dependances_visualisation()
    println("📦 INSTALLATION DES DÉPENDANCES VISUALISATION")
    println("="^60)
    
    # Liste des packages requis
    packages_requis = [
        ("Plots", "1.38"),
        ("PlotlyJS", "0.18"),
        ("JSON", "0.21"),
        ("Printf", "stdlib"),
        ("Dates", "stdlib")
    ]
    
    println("📋 Packages à installer :")
    for (nom, version) in packages_requis
        println("  - $nom (version $version)")
    end
    println()
    
    # Installation des packages
    for (nom_package, version) in packages_requis
        try
            if version == "stdlib"
                println("📚 $nom_package (bibliothèque standard) - déjà disponible")
                continue
            end
            
            println("📦 Installation de $nom_package...")
            
            # Vérifier si déjà installé
            try
                eval(:(using $(Symbol(nom_package))))
                println("✅ $nom_package déjà installé et fonctionnel")
                continue
            catch
                # Package non installé ou non fonctionnel
            end
            
            # Installer le package
            Pkg.add(nom_package)
            println("✅ $nom_package installé avec succès")
            
        catch e
            println("❌ Erreur lors de l'installation de $nom_package : $e")
            return false
        end
    end
    
    println("\n🔧 Configuration du backend PlotlyJS...")
    try
        # Charger Plots et configurer PlotlyJS
        using Plots, PlotlyJS
        plotlyjs()
        println("✅ Backend PlotlyJS configuré")
    catch e
        println("❌ Erreur configuration PlotlyJS : $e")
        return false
    end
    
    println("\n✅ INSTALLATION TERMINÉE AVEC SUCCÈS")
    println("💡 Vous pouvez maintenant utiliser la visualisation INDEX5")
    
    return true
end

"""
    tester_installation()

Teste que toutes les dépendances sont correctement installées.
"""
function tester_installation()
    println("\n🧪 TEST DE L'INSTALLATION")
    println("="^40)
    
    tests_reussis = 0
    tests_totaux = 0
    
    # Test 1 : Import des packages
    tests_totaux += 1
    try
        using Plots, PlotlyJS, JSON, Printf, Dates
        println("✅ Import des packages : RÉUSSI")
        tests_reussis += 1
    catch e
        println("❌ Import des packages : ÉCHEC ($e)")
    end
    
    # Test 2 : Configuration PlotlyJS
    tests_totaux += 1
    try
        plotlyjs()
        println("✅ Configuration PlotlyJS : RÉUSSI")
        tests_reussis += 1
    catch e
        println("❌ Configuration PlotlyJS : ÉCHEC ($e)")
    end
    
    # Test 3 : Création d'un graphique simple
    tests_totaux += 1
    try
        p = plot([1, 2, 3], [1, 4, 9], title="Test")
        println("✅ Création graphique : RÉUSSI")
        tests_reussis += 1
    catch e
        println("❌ Création graphique : ÉCHEC ($e)")
    end
    
    # Test 4 : Layout multiple
    tests_totaux += 1
    try
        p1 = plot([1, 2], [1, 4])
        p2 = plot([1, 2], [2, 3])
        p_combined = plot(p1, p2, layout=(1, 2))
        println("✅ Layout multiple : RÉUSSI")
        tests_reussis += 1
    catch e
        println("❌ Layout multiple : ÉCHEC ($e)")
    end
    
    # Résumé
    println("\n📊 RÉSULTATS DES TESTS")
    println("-"^30)
    println("Tests réussis : $tests_reussis/$tests_totaux")
    
    if tests_reussis == tests_totaux
        println("🎉 INSTALLATION PARFAITEMENT FONCTIONNELLE")
        return true
    else
        println("⚠️  INSTALLATION INCOMPLÈTE")
        println("🔧 Relancez le script ou installez manuellement les packages manquants")
        return false
    end
end

"""
    afficher_informations_systeme()

Affiche les informations système utiles pour le diagnostic.
"""
function afficher_informations_systeme()
    println("\n💻 INFORMATIONS SYSTÈME")
    println("="^35)
    println("Julia version : $(VERSION)")
    println("OS : $(Sys.KERNEL)")
    println("Architecture : $(Sys.MACHINE)")
    
    # Informations sur les packages installés
    try
        installed_packages = Pkg.installed()
        if haskey(installed_packages, "Plots")
            println("Plots.jl version : $(installed_packages["Plots"])")
        end
        if haskey(installed_packages, "PlotlyJS")
            println("PlotlyJS.jl version : $(installed_packages["PlotlyJS"])")
        end
    catch
        println("Impossible de récupérer les versions des packages")
    end
end

# Exécution automatique si le script est lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    println("🚀 INSTALLATION AUTOMATIQUE DES DÉPENDANCES")
    println("="^70)
    
    # Afficher les informations système
    afficher_informations_systeme()
    
    # Installer les dépendances
    succes_installation = installer_dependances_visualisation()
    
    if succes_installation
        # Tester l'installation
        succes_tests = tester_installation()
        
        if succes_tests
            println("\n🎯 PRÊT POUR LA VISUALISATION INDEX5")
            println("💡 Lancez maintenant : julia test_visualisation.jl")
        end
    else
        println("\n❌ INSTALLATION ÉCHOUÉE")
        println("🔧 Installez manuellement avec : Pkg.add([\"Plots\", \"PlotlyJS\"])")
    end
end

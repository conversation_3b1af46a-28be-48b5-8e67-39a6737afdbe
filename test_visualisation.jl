"""
Script de test pour la nouvelle classe VisualisateurMetriques
============================================================

Ce script teste la génération de graphiques interactifs pour l'analyse INDEX5.
"""

# Inclure le module principal
include("Analyseur.jl")

"""
    tester_visualisation_complete()

Teste la génération complète de graphiques avec données simulées.
"""
function tester_visualisation_complete()
    println("🧪 TEST DE LA VISUALISATION GRAPHIQUE")
    println("="^60)
    
    try
        # 1. Créer des données de test simulées
        println("📊 Création de données de test...")
        
        # Simuler des métriques pour 20 mains
        nb_mains = 20
        resultats_test = Vector{ResultatMainReelle{Float64}}()
        
        for i in 1:nb_mains
            # Simuler des métriques avec tendances réalistes
            metriques = MetriquesTheorique{Float64}(
                2.0 - 0.05*i + 0.1*randn(),  # CondT décroissant
                1.5 - 0.03*i + 0.08*randn(), # DivKLT décroissant
                2.2 - 0.04*i + 0.09*randn(), # CrossT décroissant
                0.1*randn(),                  # MetricT oscillant
                0.8 + 0.02*i + 0.05*randn(), # TopoT croissant
                i >= 3 ? 1.0 + 0.01*i + 0.03*randn() : 0.0, # TauxT (N/A pour mains 1-2)
                1.2 + 0.01*i + 0.04*randn(), # ShannonT croissant lent
                i >= 3 ? 2.0 + 0.05*i + 0.06*randn() : 0.0  # BlockT (N/A pour mains 1-2)
            )
            
            # Simuler des différentiels (Nothing pour main 1)
            differentiels = if i == 1
                nothing
            else
                DifferentielsPredictifs{Float64}(
                    abs(0.05 + 0.02*randn()), # diff_cond_t
                    abs(0.03 + 0.01*randn()), # diff_divkl_t
                    abs(0.04 + 0.02*randn()), # diff_cross_t
                    abs(0.1*randn()),         # diff_metric_t
                    abs(0.02 + 0.01*randn()), # diff_topo_t
                    abs(0.01 + 0.005*randn()),# diff_taux_t
                    abs(0.01 + 0.005*randn()),# diff_shannon_t
                    abs(0.05 + 0.02*randn())  # diff_block_t
                )
            end
            
            # Simuler INDEX5 aléatoire
            index5_values = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
            index5 = index5_values[rand(1:3)]
            
            # Créer le résultat
            resultat = ResultatMainReelle{Float64}(i, index5, metriques, differentiels)
            push!(resultats_test, resultat)
        end
        
        println("✅ Données de test créées : $nb_mains mains")
        
        # 2. Créer le visualisateur
        println("🎨 Création du visualisateur...")
        visualisateur = VisualisateurMetriques{Float64}(resultats_test, 999, "Test Visualisation")
        println("✅ Visualisateur créé")
        
        # 3. Générer les graphiques
        println("📈 Génération des graphiques...")
        plot_complet = generer_graphiques_analyse_complete(visualisateur)
        println("✅ Graphiques générés avec succès")
        
        # 4. Exporter vers HTML
        println("💾 Export vers HTML...")
        nom_fichier_html = exporter_graphiques_vers_html(plot_complet, 999)
        println("✅ Export réussi : $nom_fichier_html")
        
        # 5. Statistiques
        println("\n📊 STATISTIQUES DU TEST")
        println("-"^40)
        println("Nombre de mains : $nb_mains")
        println("Nombre de métriques : 8")
        println("Nombre de différentiels : 8")
        println("Nombre total de graphiques : 16")
        println("Fichier généré : $nom_fichier_html")
        
        return true
        
    catch e
        println("❌ ERREUR LORS DU TEST : $e")
        println("📋 Stack trace :")
        for (exc, bt) in Base.catch_stack()
            showerror(stdout, exc, bt)
            println()
        end
        return false
    end
end

"""
    tester_couleurs_et_layout()

Teste spécifiquement les couleurs et le layout des graphiques.
"""
function tester_couleurs_et_layout()
    println("\n🎨 TEST DES COULEURS ET LAYOUT")
    println("="^50)
    
    try
        # Créer un visualisateur minimal
        metriques_test = MetriquesTheorique{Float64}(1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0)
        resultat_test = ResultatMainReelle{Float64}(1, "0_A_BANKER", metriques_test, nothing)
        
        visualisateur = VisualisateurMetriques{Float64}([resultat_test], 1)
        
        # Vérifier les couleurs
        println("🎨 Couleurs des métriques :")
        for (metrique, couleur) in visualisateur.couleurs_metriques
            println("  $metrique : $couleur")
        end
        
        println("\n🎨 Couleurs des différentiels :")
        for (metrique, couleur) in visualisateur.couleurs_differentiels
            println("  Δ $metrique : $couleur")
        end
        
        println("✅ Test des couleurs réussi")
        return true
        
    catch e
        println("❌ ERREUR TEST COULEURS : $e")
        return false
    end
end

# Exécution des tests si le script est lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    println("🚀 LANCEMENT DES TESTS DE VISUALISATION")
    println("="^70)
    
    # Test 1 : Couleurs et layout
    succes_couleurs = tester_couleurs_et_layout()
    
    # Test 2 : Visualisation complète
    succes_complet = tester_visualisation_complete()
    
    # Résumé final
    println("\n🏁 RÉSUMÉ DES TESTS")
    println("="^30)
    println("Test couleurs : $(succes_couleurs ? "✅ RÉUSSI" : "❌ ÉCHEC")")
    println("Test complet : $(succes_complet ? "✅ RÉUSSI" : "❌ ÉCHEC")")
    
    if succes_couleurs && succes_complet
        println("\n🎉 TOUS LES TESTS RÉUSSIS !")
        println("💡 La classe VisualisateurMetriques est opérationnelle")
    else
        println("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        println("🔧 Vérifiez les dépendances : Plots.jl et PlotlyJS.jl")
    end
end
